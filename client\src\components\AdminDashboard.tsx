// @ts-ignore
import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import LanguageSwitcher from './LanguageSwitcher';

interface MaterialItem {
  _id: string;
  material: string;
  quantityOrdered: number;
  quantityDelivered: number;
  unit: string;
  ratePerUnit: number;
  isOverDelivered: boolean;
}

interface DeliveryLocation {
  _id: string;
  address: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  materials: MaterialItem[];
  status: 'pending' | 'completed';
  completedAt?: string;
  deliveryPhoto?: string;
  signature?: string;
}

interface PurchaseOrder {
  _id: string;
  orderNumber: string;
  client: string;
  title: string;
  description?: string;
  pickupLocation: {
    address: string;
    coordinates: {
      lat: number;
      lng: number;
    };
  };
  deliveryLocations: DeliveryLocation[];
  pickupDate: string;
  deliveryDate: string;
  weight: number;
  haulingRate: number;
  status: string;
  createdBy: {
    username: string;
    firstName: string;
    lastName: string;
  };
  assignedTo?: {
    username: string;
    firstName: string;
    lastName: string;
  };
  acceptedAt?: string;
  createdAt: string;
}

interface Transaction {
  _id: string;
  purchaseOrderId?: {
    _id: string;
    orderNumber: string;
    client: string;
    title: string;
    haulingRate: number;
  } | null;
  orderNumber: string;
  trucker?: {
    _id: string;
    username: string;
    firstName: string;
    lastName: string;
  } | null;
  deliveryLocationId: string;
  deliveryAddress: string;
  materialsDelivered: {
    material: string;
    quantityDelivered: number;
    unit: string;
    ratePerUnit: number;
  }[];
  pickupPhoto: string;
  pickupSignature: string;
  deliveryPhoto: string;
  signature: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  notes?: string;
  completedAt: string;
  createdAt: string;
}

const AdminDashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const { t } = useTranslation();
  const [orders, setOrders] = useState<PurchaseOrder[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [activeTab, setActiveTab] = useState<'orders' | 'transactions'>('orders');

  // Get today's date in YYYY-MM-DD format
  const getTodayDate = () => {
    const today = new Date();
    return today.toISOString().split('T')[0];
  };

  interface DeliveryLocationForm {
    address: string;
    materials: {
      material: string;
      quantity: string;
      unit: string;
      ratePerUnit: string;
    }[];
  }

  const [formData, setFormData] = useState({
    client: '',
    title: '',
    description: '',
    pickupLocation: '1605 Renaissance Commons Blvd., Boynton Beach, FL 33426',
    deliveryLocations: [{
      address: '',
      materials: [{
        material: '',
        quantity: '',
        unit: 'tons',
        ratePerUnit: ''
      }]
    }] as DeliveryLocationForm[],
    pickupDate: getTodayDate(),
    deliveryDate: '',

    notes: ''
  });

  const availableDeliveryLocations = [
    '1500 Gateway Blvd., Boynton Beach, FL 33426',
    '2000 Corporate Blvd., Boca Raton, FL 33431',
    '1800 Congress Ave., Delray Beach, FL 33445',
    '2200 Glades Rd., Boca Raton, FL 33431',
    '1400 Town Center Cir., Boca Raton, FL 33486',
    '3000 Yamato Rd., Boca Raton, FL 33434',
    'Dordrecht, Netherlands'
  ];



  const API_BASE_URL = '/api';

  useEffect(() => {
    fetchOrders();
    if (activeTab === 'transactions') {
      fetchTransactions();
    }
  }, [activeTab]);

  const fetchOrders = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/purchase-orders`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (response.data.success) {
        setOrders(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchTransactions = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/purchase-orders/admin/transactions`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.data.success) {
        setTransactions(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
    }
  };

  const handleCreateOrder = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.client || !formData.title || formData.deliveryLocations.length === 0 ||
        !formData.pickupDate || !formData.deliveryDate) {
      alert('Please fill in all required fields and at least one delivery location');
      return;
    }

    // Validate that all materials have rates
    for (const location of formData.deliveryLocations) {
      for (const material of location.materials) {
        if (!material.material || !material.quantity || !material.ratePerUnit) {
          alert('Please fill in all material details including rates per unit');
          return;
        }
      }
    }

    // Validate delivery locations and materials
    for (const location of formData.deliveryLocations) {
      if (!location.address) {
        alert('Please select an address for all delivery locations');
        return;
      }
      if (location.materials.length === 0) {
        alert('Each delivery location must have at least one material');
        return;
      }
      for (const material of location.materials) {
        if (!material.material || !material.quantity || !material.unit) {
          alert('Please fill in all material details (type, quantity, unit)');
          return;
        }
      }
    }

    try {
      const orderData = {
        client: formData.client.trim(),
        title: formData.title.trim(),
        description: formData.description.trim(),
        pickupLocation: formData.pickupLocation,
        deliveryLocations: formData.deliveryLocations.map(location => ({
          ...location,
          materials: location.materials.map(material => ({
            material: material.material,
            quantity: parseFloat(material.quantity),
            unit: material.unit,
            ratePerUnit: parseFloat(material.ratePerUnit)
          }))
        })),
        pickupDate: formData.pickupDate,
        deliveryDate: formData.deliveryDate,
        notes: formData.notes.trim()
      };

      console.log('Sending order data:', orderData);

      const response = await axios.post(`${API_BASE_URL}/purchase-orders`, orderData);
      console.log('Response:', response.data);

      if (response.data.success) {
        setOrders([response.data.data, ...orders]);
        setShowCreateForm(false);
        // Reset form
        setFormData({
          client: '',
          title: '',
          description: '',
          pickupLocation: '1605 Renaissance Commons Blvd., Boynton Beach, FL 33426',
          deliveryLocations: [{
            address: '',
            materials: [{
              material: '',
              quantity: '',
              unit: 'tons',
              ratePerUnit: ''
            }]
          }],
          pickupDate: getTodayDate(),
          deliveryDate: '',
          notes: ''
        });
        alert('Purchase order created successfully!');
      }
    } catch (error: any) {
      console.error('Error creating order:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Unknown error occurred';
      alert(`Error creating purchase order: ${errorMessage}`);
    }
  };

  const handleDeleteAllOrders = async () => {
    if (!window.confirm('Are you sure you want to delete ALL purchase orders? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await axios.delete(`${API_BASE_URL}/purchase-orders/admin/delete-all`);

      if (response.data.success) {
        setOrders([]);
        alert(`Successfully deleted ${response.data.deletedCount} purchase orders`);
      }
    } catch (error: any) {
      console.error('Error deleting all orders:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Unknown error occurred';
      alert(`Error deleting orders: ${errorMessage}`);
    }
  };

  const handleDeleteTransaction = async (transactionId: string) => {
    if (!window.confirm('Are you sure you want to delete this transaction? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await axios.delete(`${API_BASE_URL}/purchase-orders/admin/transactions/${transactionId}`);

      if (response.data.success) {
        setTransactions(transactions.filter(t => t._id !== transactionId));
        alert('Transaction deleted successfully');
      }
    } catch (error: any) {
      console.error('Error deleting transaction:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Unknown error occurred';
      alert(`Error deleting transaction: ${errorMessage}`);
    }
  };

  const handleDeleteAllTransactions = async () => {
    if (!window.confirm('Are you sure you want to delete ALL transactions? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await axios.delete(`${API_BASE_URL}/purchase-orders/admin/transactions/delete-all`);

      if (response.data.success) {
        setTransactions([]);
        alert(`Successfully deleted ${response.data.deletedCount} transactions`);
      }
    } catch (error: any) {
      console.error('Error deleting all transactions:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Unknown error occurred';
      alert(`Error deleting transactions: ${errorMessage}`);
    }
  };

  const addDeliveryLocation = () => {
    const newLocation: DeliveryLocationForm = {
      address: '',
      materials: [{
        material: '',
        quantity: '',
        unit: 'tons',
        ratePerUnit: ''
      }]
    };
    setFormData({
      ...formData,
      deliveryLocations: [...formData.deliveryLocations, newLocation]
    });
  };

  const removeDeliveryLocation = (index: number) => {
    const newLocations = formData.deliveryLocations.filter((_, i) => i !== index);
    setFormData({...formData, deliveryLocations: newLocations});
  };

  const updateDeliveryLocation = (index: number, field: string, value: string) => {
    const newLocations = [...formData.deliveryLocations];
    (newLocations[index] as any)[field] = value;
    setFormData({...formData, deliveryLocations: newLocations});
  };

  const addMaterial = (locationIndex: number) => {
    const newLocations = [...formData.deliveryLocations];
    newLocations[locationIndex].materials.push({
      material: '',
      quantity: '',
      unit: 'tons',
      ratePerUnit: ''
    });
    setFormData({...formData, deliveryLocations: newLocations});
  };

  const removeMaterial = (locationIndex: number, materialIndex: number) => {
    const newLocations = [...formData.deliveryLocations];
    newLocations[locationIndex].materials = newLocations[locationIndex].materials.filter((_, i) => i !== materialIndex);
    setFormData({...formData, deliveryLocations: newLocations});
  };

  const updateMaterial = (locationIndex: number, materialIndex: number, field: string, value: string) => {
    const newLocations = [...formData.deliveryLocations];
    (newLocations[locationIndex].materials[materialIndex] as any)[field] = value;
    setFormData({...formData, deliveryLocations: newLocations});
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-warning';
      case 'accepted': return 'bg-success';
      case 'pickup_completed': return 'bg-info';
      case 'en_route': return 'bg-info';
      case 'delivery_in_progress': return 'bg-primary';
      case 'delivered': return 'bg-success';
      case 'cancelled': return 'bg-danger';
      default: return 'bg-secondary';
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center vh-100">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      {/* Header */}
      <nav className="navbar navbar-expand-lg navbar-dark bg-success mb-4">
        <div className="container-fluid">
          <span className="navbar-brand">
            <i className="bi bi-person-gear me-2"></i>
            {t('dashboard.adminDashboard')}
          </span>
          <div className="navbar-nav ms-auto">
            <span className="navbar-text me-3">
              {t('dashboard.welcome')}, {user?.firstName} {user?.lastName}
            </span>
            <div className="me-3">
              <LanguageSwitcher />
            </div>
            <button className="btn btn-outline-light" onClick={logout}>
              <i className="bi bi-box-arrow-right me-1"></i>
              {t('auth.logout')}
            </button>
          </div>
        </div>
      </nav>

      <div className="container-fluid">
        {/* Action Bar */}
        <div className="row mb-4">
          <div className="col">
            <div className="d-flex justify-content-between align-items-center">
              <h2>{t('dashboard.adminDashboard')}</h2>
              <div>
                <button
                  className="btn btn-danger me-2"
                  onClick={handleDeleteAllOrders}
                  title={t('orders.deleteAllOrders')}
                >
                  <i className="bi bi-trash me-2"></i>
                  {t('orders.deleteAllOrders')}
                </button>
                <button
                  className="btn btn-success"
                  onClick={() => setShowCreateForm(true)}
                >
                  <i className="bi bi-plus-circle me-2"></i>
                  {t('orders.createNewOrder')}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="row mb-4">
          <div className="col">
            <ul className="nav nav-tabs">
              <li className="nav-item">
                <button
                  className={`nav-link ${activeTab === 'orders' ? 'active' : ''}`}
                  onClick={() => setActiveTab('orders')}
                >
                  <i className="bi bi-truck me-2"></i>
                  {t('orders.purchaseOrders')}
                </button>
              </li>
              <li className="nav-item">
                <button
                  className={`nav-link ${activeTab === 'transactions' ? 'active' : ''}`}
                  onClick={() => setActiveTab('transactions')}
                >
                  <i className="bi bi-receipt me-2"></i>
                  {t('transactions.transactions')}
                </button>
              </li>
            </ul>
          </div>
        </div>



        {/* Tab Content */}
        {activeTab === 'orders' && (
          <>
            {/* Statistics */}
            <div className="row mb-4">
              <div className="col-12 col-md-3 mb-3 mb-md-0">
                <div className="card bg-primary text-white">
                  <div className="card-body text-center">
                    <h6 className="card-title">Total Orders</h6>
                    <h3 className="mb-0">{orders.length}</h3>
                  </div>
                </div>
              </div>
              <div className="col-12 col-md-3 mb-3 mb-md-0">
                <div className="card bg-warning text-white">
                  <div className="card-body text-center">
                    <h6 className="card-title">Pending Orders</h6>
                    <h3 className="mb-0">{orders.filter(o => o.status === 'pending').length}</h3>
                  </div>
                </div>
              </div>
              <div className="col-12 col-md-3 mb-3 mb-md-0">
                <div className="card bg-success text-white">
                  <div className="card-body text-center">
                    <h6 className="card-title">Completed Orders</h6>
                    <h3 className="mb-0">{orders.filter(o => o.status === 'delivered').length}</h3>
                  </div>
                </div>
              </div>
              <div className="col-12 col-md-3">
                <div className="card bg-info text-white">
                  <div className="card-body text-center">
                    <h6 className="card-title">Total Transactions</h6>
                    <h3 className="mb-0">{transactions.length}</h3>
                  </div>
                </div>
              </div>
            </div>

            {/* Orders Table */}
            <div className="card">
              <div className="card-body">
                <div className="table-responsive">
                  <table className="table table-striped">
                    <thead>
                      <tr>
                        <th>Order #</th>
                        <th>Client</th>
                        <th>Title</th>
                        <th>Pickup Location</th>
                        <th>Delivery Locations</th>
                        <th>Status</th>
                        <th>Assigned To</th>
                        <th>Total Value</th>
                        <th>Created</th>
                      </tr>
                    </thead>
                    <tbody>
                      {orders.map((order) => (
                        <tr key={order._id}>
                          <td>{order.orderNumber}</td>
                          <td>{order.client}</td>
                          <td>{order.title}</td>
                          <td>{typeof order.pickupLocation === 'object' ? order.pickupLocation.address : order.pickupLocation}</td>
                          <td>
                            {order.deliveryLocations && order.deliveryLocations.length > 0 ? (
                              <div>
                                {order.deliveryLocations.map((location, index) => (
                                  <div key={location._id || index} className="mb-2 p-2 border rounded">
                                    <div className="d-flex align-items-center mb-1">
                                      <small className={`badge ${location.status === 'completed' ? 'bg-success' : 'bg-secondary'} me-2`}>
                                        {location.status === 'completed' ? '✓' : '○'}
                                      </small>
                                      <small className="fw-bold">{location.address}</small>
                                    </div>
                                    {location.materials && location.materials.length > 0 && (
                                      <div className="ms-3">
                                        {location.materials.map((material: any, matIndex: number) => (
                                          <div key={matIndex} className="small text-muted">
                                            • {material.quantityDelivered || 0}/{material.quantityOrdered} {material.unit} of {material.material}
                                            {material.isOverDelivered && <span className="text-warning ms-1">⚠️ Over-delivered</span>}
                                          </div>
                                        ))}
                                      </div>
                                    )}
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <span className="text-muted">No delivery locations</span>
                            )}
                          </td>
                          <td>
                            <span className={`badge ${getStatusBadgeClass(order.status)}`}>
                              {order.status.replace('_', ' ').toUpperCase()}
                            </span>
                          </td>
                          <td>
                            {order.assignedTo ?
                              `${order.assignedTo.firstName} ${order.assignedTo.lastName}` :
                              'Unassigned'
                            }
                          </td>
                          <td>
                            {order.deliveryLocations.reduce((total, location) =>
                              total + location.materials.reduce((locTotal, material) =>
                                locTotal + (material.quantityOrdered * (material.ratePerUnit || 0)), 0
                              ), 0
                            ).toLocaleString('en-US', { style: 'currency', currency: 'USD' })}
                          </td>
                          <td>
                            <div>{new Date(order.createdAt).toLocaleDateString()}</div>
                            <small className="text-muted">{new Date(order.createdAt).toLocaleTimeString()}</small>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Transactions Tab */}
        {activeTab === 'transactions' && (
          <>
            {/* Transaction Analytics */}
            <div className="row mb-4">
              <div className="col-12 col-md-4 mb-3 mb-md-0">
                <div className="card bg-success text-white">
                  <div className="card-body text-center">
                    <h6 className="card-title">Total Revenue</h6>
                    <h3 className="mb-0">
                      {transactions.reduce((total, t) =>
                        total + (t.materialsDelivered?.reduce((sum, m) =>
                          sum + ((m.quantityDelivered || 0) * (m.ratePerUnit || 0)), 0) || 0), 0
                      ).toLocaleString('en-US', { style: 'currency', currency: 'USD' })}
                    </h3>
                  </div>
                </div>
              </div>
              <div className="col-12 col-md-4 mb-3 mb-md-0">
                <div className="card bg-info text-white">
                  <div className="card-body text-center">
                    <h6 className="card-title">This Month</h6>
                    <h3 className="mb-0">
                      {transactions.filter(t => {
                        const transactionDate = new Date(t.completedAt);
                        const now = new Date();
                        return transactionDate.getMonth() === now.getMonth() &&
                               transactionDate.getFullYear() === now.getFullYear();
                      }).length}
                    </h3>
                  </div>
                </div>
              </div>
              <div className="col-12 col-md-4">
                <div className="card bg-warning text-white">
                  <div className="card-body text-center">
                    <h6 className="card-title">Avg per Transaction</h6>
                    <h3 className="mb-0">
                      {transactions.length > 0 ?
                        (transactions.reduce((total, t) =>
                          total + (t.materialsDelivered?.reduce((sum, m) =>
                            sum + ((m.quantityDelivered || 0) * (m.ratePerUnit || 0)), 0) || 0), 0
                        ) / transactions.length).toLocaleString('en-US', { style: 'currency', currency: 'USD' })
                        : '$0.00'
                      }
                    </h3>
                  </div>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="card-body">
                <div className="d-flex justify-content-between align-items-center">
                  <h5 className="card-title mb-0">
                    <i className="bi bi-receipt me-2"></i>
                    {t('transactions.transactionHistory')} ({transactions.length})
                  </h5>
                  <button
                    className="btn btn-danger btn-sm"
                    onClick={handleDeleteAllTransactions}
                    disabled={transactions.length === 0}
                  >
                    <i className="bi bi-trash me-1"></i>
                    Delete All Transactions
                  </button>
                </div>
              <div className="table-responsive">
                <table className="table table-striped">
                  <thead>
                    <tr>
                      <th>{t('transactions.date')}</th>
                      <th>{t('transactions.orderNumber')}</th>
                      <th>{t('transactions.trucker')}</th>
                      <th>{t('transactions.client')}</th>
                      <th>{t('transactions.deliveryAddress')}</th>
                      <th>{t('transactions.materialsDelivered')}</th>
                      <th>{t('transactions.totalValue')}</th>
                      <th>{t('transactions.actions')}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {transactions.length === 0 ? (
                      <tr>
                        <td colSpan={8} className="text-center text-muted">
                          {t('transactions.noTransactionsFound')}
                        </td>
                      </tr>
                    ) : (
                      transactions.map((transaction) => (
                        <tr key={transaction._id}>
                          <td>
                            <div>{new Date(transaction.completedAt).toLocaleDateString()}</div>
                            <small className="text-muted">{new Date(transaction.completedAt).toLocaleTimeString()}</small>
                          </td>
                          <td>{transaction.orderNumber || 'N/A'}</td>
                          <td>
                            {transaction.trucker ?
                              `${transaction.trucker.firstName || ''} ${transaction.trucker.lastName || ''}`.trim() || 'Unknown'
                              : 'Unknown'
                            }
                          </td>
                          <td>
                            {transaction.purchaseOrderId && transaction.purchaseOrderId.client ?
                              transaction.purchaseOrderId.client
                              : 'N/A'
                            }
                          </td>
                          <td>{transaction.deliveryAddress || 'N/A'}</td>
                          <td>
                            {transaction.materialsDelivered && transaction.materialsDelivered.length > 0 ?
                              transaction.materialsDelivered.map((material, index) => (
                                <div key={index} className="small">
                                  {material.quantityDelivered || 0} {material.unit || ''} of {material.material || 'Unknown'}
                                </div>
                              ))
                              : <span className="text-muted">No materials</span>
                            }
                          </td>
                          <td>
                            {transaction.materialsDelivered && transaction.materialsDelivered.length > 0 ?
                              transaction.materialsDelivered.reduce((total, material) =>
                                total + ((material.quantityDelivered || 0) * (material.ratePerUnit || 0)), 0
                              ).toLocaleString('en-US', { style: 'currency', currency: 'USD' })
                              : '$0.00'
                            }
                          </td>
                          <td>
                            <div className="d-flex gap-1">
                              <button
                                className="btn btn-sm btn-outline-info"
                                onClick={() => {
                                  const modal = document.createElement('div');
                                  modal.innerHTML = `
                                    <div class="modal show d-block" style="background-color: rgba(0,0,0,0.8);">
                                      <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                          <div class="modal-header">
                                            <h5 class="modal-title">Pickup Photo</h5>
                                            <button type="button" class="btn-close" onclick="this.closest('.modal').remove()"></button>
                                          </div>
                                          <div class="modal-body text-center">
                                            <img src="${transaction.pickupPhoto}" style="max-width: 100%; max-height: 70vh;" class="img-fluid" />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  `;
                                  document.body.appendChild(modal);
                                }}
                                title="View Pickup Photo"
                              >
                                <i className="bi bi-camera"></i> Pickup
                              </button>
                              <button
                                className="btn btn-sm btn-outline-primary"
                                onClick={() => {
                                  const modal = document.createElement('div');
                                  modal.innerHTML = `
                                    <div class="modal show d-block" style="background-color: rgba(0,0,0,0.8);">
                                      <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                          <div class="modal-header">
                                            <h5 class="modal-title">${t('transactions.deliveryPhoto')}</h5>
                                            <button type="button" class="btn-close" onclick="this.closest('.modal').remove()"></button>
                                          </div>
                                          <div class="modal-body text-center">
                                            <img src="${transaction.deliveryPhoto}" style="max-width: 100%; max-height: 70vh;" class="img-fluid" />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  `;
                                  document.body.appendChild(modal);
                                }}
                                title={t('transactions.viewPhoto')}
                              >
                                <i className="bi bi-image"></i> Delivery
                              </button>
                              <button
                                className="btn btn-sm btn-outline-success"
                                onClick={() => {
                                  const modal = document.createElement('div');
                                  modal.innerHTML = `
                                    <div class="modal show d-block" style="background-color: rgba(0,0,0,0.8);">
                                      <div class="modal-dialog">
                                        <div class="modal-content">
                                          <div class="modal-header">
                                            <h5 class="modal-title">Pickup Signature</h5>
                                            <button type="button" class="btn-close" onclick="this.closest('.modal').remove()"></button>
                                          </div>
                                          <div class="modal-body text-center">
                                            <img src="${transaction.pickupSignature}" style="max-width: 100%; max-height: 50vh;" class="img-fluid border" />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  `;
                                  document.body.appendChild(modal);
                                }}
                                title="View Pickup Signature"
                              >
                                <i className="bi bi-pen"></i> P-Sig
                              </button>
                              <button
                                className="btn btn-sm btn-outline-secondary"
                                onClick={() => {
                                  const modal = document.createElement('div');
                                  modal.innerHTML = `
                                    <div class="modal show d-block" style="background-color: rgba(0,0,0,0.8);">
                                      <div class="modal-dialog">
                                        <div class="modal-content">
                                          <div class="modal-header">
                                            <h5 class="modal-title">${t('transactions.deliverySignature')}</h5>
                                            <button type="button" class="btn-close" onclick="this.closest('.modal').remove()"></button>
                                          </div>
                                          <div class="modal-body text-center">
                                            <img src="${transaction.signature}" style="max-width: 100%; max-height: 50vh;" class="img-fluid border" />
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  `;
                                  document.body.appendChild(modal);
                                }}
                                title={t('transactions.viewSignature')}
                              >
                                <i className="bi bi-pen"></i> D-Sig
                              </button>
                              <button
                                className="btn btn-sm btn-outline-danger"
                                onClick={() => handleDeleteTransaction(transaction._id)}
                                title="Delete Transaction"
                              >
                                <i className="bi bi-trash"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          </>
        )}
      </div>

      {/* Create Order Modal */}
      {showCreateForm && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-lg">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Create New Purchase Order</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowCreateForm(false)}
                ></button>
              </div>
              <form onSubmit={handleCreateOrder}>
                <div className="modal-body">
                  <div className="row">
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">Client *</label>
                        <input
                          type="text"
                          className="form-control"
                          value={formData.client}
                          onChange={(e) => setFormData({...formData, client: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">Title *</label>
                        <input
                          type="text"
                          className="form-control"
                          value={formData.title}
                          onChange={(e) => setFormData({...formData, title: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                  </div>

                  <div className="mb-3">
                    <label className="form-label">Description</label>
                    <textarea
                      className="form-control"
                      rows={3}
                      value={formData.description}
                      onChange={(e) => setFormData({...formData, description: e.target.value})}
                    ></textarea>
                  </div>

                  <div className="row">
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">Pickup Location *</label>
                        <select
                          className="form-control"
                          value={formData.pickupLocation}
                          onChange={(e) => setFormData({...formData, pickupLocation: e.target.value})}
                          required
                        >
                          <option value="1605 Renaissance Commons Blvd., Boynton Beach, FL 33426">
                            1605 Renaissance Commons Blvd., Boynton Beach, FL 33426
                          </option>
                          <option value="Dordrecht, Netherlands">Dordrecht, Netherlands</option>  {/* Add this */}
                        </select>
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="mb-3">
                        <div className="d-flex justify-content-between align-items-center mb-2">
                          <label className="form-label mb-0">Delivery Locations & Materials *</label>
                          <button
                            type="button"
                            className="btn btn-sm btn-outline-primary"
                            onClick={addDeliveryLocation}
                          >
                            <i className="bi bi-plus"></i> Add Location
                          </button>
                        </div>

                        {formData.deliveryLocations.map((location, locationIndex) => (
                          <div key={locationIndex} className="border rounded p-3 mb-3">
                            <div className="d-flex justify-content-between align-items-center mb-2">
                              <h6 className="mb-0">Location {locationIndex + 1}</h6>
                              {formData.deliveryLocations.length > 1 && (
                                <button
                                  type="button"
                                  className="btn btn-sm btn-outline-danger"
                                  onClick={() => removeDeliveryLocation(locationIndex)}
                                >
                                  <i className="bi bi-trash"></i>
                                </button>
                              )}
                            </div>

                            <div className="mb-3">
                              <label className="form-label">Address *</label>
                              <select
                                className="form-control"
                                value={location.address}
                                onChange={(e) => updateDeliveryLocation(locationIndex, 'address', e.target.value)}
                                required
                              >
                                <option value="">Select delivery address...</option>
                                {availableDeliveryLocations.map((addr) => (
                                  <option key={addr} value={addr}>{addr}</option>
                                ))}
                              </select>
                            </div>

                            <div className="mb-2">
                              <div className="d-flex justify-content-between align-items-center">
                                <label className="form-label mb-0">Materials *</label>
                                <button
                                  type="button"
                                  className="btn btn-sm btn-outline-secondary"
                                  onClick={() => addMaterial(locationIndex)}
                                >
                                  <i className="bi bi-plus"></i> Add Material
                                </button>
                              </div>
                            </div>

                            {location.materials.map((material, materialIndex) => (
                              <div key={materialIndex} className="row mb-2">
                                <div className="col-md-4">
                                  <select
                                    className="form-control form-control-sm"
                                    value={material.material}
                                    onChange={(e) => updateMaterial(locationIndex, materialIndex, 'material', e.target.value)}
                                    required
                                  >
                                    <option value="">Select material...</option>
                                    <optgroup label="🏗️ Aggregates & Base">
                                      <option value="Crushed Concrete">Crushed Concrete</option>
                                      <option value="Crushed Asphalt">Crushed Asphalt</option>
                                      <option value="ABC (Aggregate Base Course)">ABC</option>
                                      <option value="Road Base">Road Base</option>
                                      <option value="#57 Stone">#57 Stone</option>
                                      <option value="#89 Stone">#89 Stone</option>
                                    </optgroup>
                                    <optgroup label="🏗️ Sand & Soil">
                                      <option value="Fill Sand">Fill Sand</option>
                                      <option value="Mason Sand">Mason Sand</option>
                                      <option value="Concrete Sand">Concrete Sand</option>
                                      <option value="Topsoil – Screened">Topsoil – Screened</option>
                                    </optgroup>
                                    <optgroup label="♻️ Recycled">
                                      <option value="Recycled Asphalt Millings">Recycled Asphalt</option>
                                      <option value="Recycled Concrete Base">Recycled Concrete</option>
                                    </optgroup>
                                    <optgroup label="🗑️ Haul-Off">
                                      <option value="Concrete Demo (Broken)">Concrete Demo</option>
                                      <option value="Mixed Construction Debris">Mixed Debris</option>
                                    </optgroup>
                                  </select>
                                </div>
                                <div className="col-md-2">
                                  <input
                                    type="number"
                                    className="form-control form-control-sm"
                                    placeholder="Qty"
                                    value={material.quantity}
                                    onChange={(e) => updateMaterial(locationIndex, materialIndex, 'quantity', e.target.value)}
                                    required
                                  />
                                </div>
                                <div className="col-md-2">
                                  <select
                                    className="form-control form-control-sm"
                                    value={material.unit}
                                    onChange={(e) => updateMaterial(locationIndex, materialIndex, 'unit', e.target.value)}
                                    required
                                  >
                                    <option value="tons">Tons</option>
                                    <option value="cubic yards">Cubic Yards</option>
                                    <option value="loads">Loads</option>
                                    <option value="gallons">Gallons</option>
                                  </select>
                                </div>
                                <div className="col-md-3">
                                  <input
                                    type="number"
                                    className="form-control form-control-sm"
                                    placeholder="Rate per unit ($)"
                                    step="0.01"
                                    value={material.ratePerUnit}
                                    onChange={(e) => updateMaterial(locationIndex, materialIndex, 'ratePerUnit', e.target.value)}
                                    required
                                  />
                                </div>
                                <div className="col-md-1">
                                  {location.materials.length > 1 && (
                                    <button
                                      type="button"
                                      className="btn btn-sm btn-outline-danger"
                                      onClick={() => removeMaterial(locationIndex, materialIndex)}
                                    >
                                      <i className="bi bi-trash"></i>
                                    </button>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="row">
                    <div className="col-md-3">
                      <div className="mb-3">
                        <label className="form-label">Pickup Date *</label>
                        <input
                          type="date"
                          className="form-control"
                          value={formData.pickupDate}
                          onChange={(e) => setFormData({...formData, pickupDate: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-3">
                      <div className="mb-3">
                        <label className="form-label">Delivery Date *</label>
                        <input
                          type="date"
                          className="form-control"
                          value={formData.deliveryDate}
                          onChange={(e) => setFormData({...formData, deliveryDate: e.target.value})}
                          required
                        />
                      </div>
                    </div>

                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn btn-secondary"
                    onClick={() => setShowCreateForm(false)}
                  >
                    Cancel
                  </button>
                  <button type="submit" className="btn btn-success">
                    Create Order
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminDashboard;
