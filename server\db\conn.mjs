import mongoose from 'mongoose'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const connectionString = process.env.ATLAS_URI || 'mongodb+srv://thomasvanrossum5:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0'
console.log('MongoDB connection string:', connectionString ? 'Found' : 'Not found')
console.log('Environment variables loaded:', !!process.env.ATLAS_URI)

mongoose
  .connect(connectionString, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  })
  .then(() => {
    console.log('Connected to MongoDB')
  })
  .catch((error) => {
    console.error('Error connecting to MongoDB', error)
  })
