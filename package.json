{"name": "vitejs-stack-mern-quickstart", "description": "This is a quick start project template that combines the power of Vite.js and the MERN (MongoDB, Express, React, Node.js) stack. It provides a solid foundation for building modern web applications with a fast development experience.", "author": "To<PERSON>", "version": "1.0.0", "main": "index.js", "license": "MIT", "devDependencies": {"@types/react-signature-canvas": "^1.0.7", "concurrently": "^8.2.0", "prettier": "^3.0.0"}, "scripts": {"dev": "concurrently \"cd server && npm run dev\" \"cd client && npm run dev\" \"cd client && npm run sass\"", "dev-client": "concurrently \"cd client && npm run dev\" \"cd client && npm run sass\"", "build": "cd client && npm run build", "start": "cd server && npm start", "deploy": "npm run build && npm start"}}