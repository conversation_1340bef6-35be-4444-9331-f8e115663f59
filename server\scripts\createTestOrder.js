import mongoose from 'mongoose';
import PurchaseOrder from '../models/PurchaseOrder.js';
import User from '../models/User.js';
import 'dotenv/config';

const connectionString = process.env.ATLAS_URI || '';

async function createTestOrder() {
  try {
    // Connect to MongoDB
    await mongoose.connect(connectionString, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('Connected to MongoDB');

    // Find admin user
    const admin = await User.findOne({ role: 'admin' });
    if (!admin) {
      console.log('No admin user found. Please create test users first.');
      return;
    }

    // Check if test order already exists
    const existingOrder = await PurchaseOrder.findOne({ client: 'Test Client' });
    if (existingOrder) {
      console.log('Test order already exists:', existingOrder.orderNumber);
      return;
    }

    // Create test order
    const testOrder = new PurchaseOrder({
      client: 'Test Client',
      title: 'Test Construction Project',
      description: 'Test order for debugging purposes',
      pickupLocation: {
        address: '1605 Renaissance Commons Blvd., Boynton Beach, FL 33426',
        coordinates: { lat: 0, lng: 0 } // Will be set by pre-save middleware
      },
      deliveryLocations: [
        {
          address: '1500 Gateway Blvd., Boynton Beach, FL 33426',
          coordinates: { lat: 0, lng: 0 }, // Will be set by pre-save middleware
          materials: [
            {
              material: 'Concrete Sand',
              quantityOrdered: 10,
              quantityDelivered: 0,
              unit: 'tons',
              ratePerUnit: 50,
              isOverDelivered: false
            },
            {
              material: '#57 Stone',
              quantityOrdered: 5,
              quantityDelivered: 0,
              unit: 'tons',
              ratePerUnit: 45,
              isOverDelivered: false
            }
          ],
          status: 'pending'
        },
        {
          address: '2000 Corporate Blvd., Boca Raton, FL 33431',
          coordinates: { lat: 0, lng: 0 }, // Will be set by pre-save middleware
          materials: [
            {
              material: 'ABC (Aggregate Base Course)',
              quantityOrdered: 15,
              quantityDelivered: 0,
              unit: 'tons',
              ratePerUnit: 40,
              isOverDelivered: false
            }
          ],
          status: 'pending'
        }
      ],
      pickupDate: new Date(),
      deliveryDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
      notes: 'Test order for debugging the trucker dashboard',
      createdBy: admin._id
    });

    await testOrder.save();
    console.log('Test order created successfully!');
    console.log('Order Number:', testOrder.orderNumber);
    console.log('Client:', testOrder.client);
    console.log('Delivery Locations:', testOrder.deliveryLocations.length);
    console.log('All delivery locations have status "pending"');

  } catch (error) {
    console.error('Error creating test order:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

createTestOrder();
