import mongoose from 'mongoose';

const deliveredMaterialSchema = new mongoose.Schema({
  material: {
    type: String,
    required: true
  },
  quantityDelivered: {
    type: Number,
    required: true,
    min: 0
  },
  unit: {
    type: String,
    required: true,
    enum: ['tons', 'cubic yards', 'loads', 'gallons']
  },
  ratePerUnit: {
    type: Number,
    required: true,
    min: 0
  }
}, { _id: false });

const transactionSchema = new mongoose.Schema({
  purchaseOrderId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'PurchaseOrder',
    required: true
  },
  orderNumber: {
    type: String,
    required: true
  },
  trucker: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  deliveryLocationId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true
  },
  deliveryAddress: {
    type: String,
    required: true
  },
  materialsDelivered: [deliveredMaterialSchema],
  pickupPhoto: {
    type: String,
    required: true
  },
  pickupSignature: {
    type: String,
    required: true
  },
  deliveryPhoto: {
    type: String,
    required: false, // Not required for partial transactions
    default: null
  },
  deliverySignature: {
    type: String,
    required: false, // Not required for partial transactions
    default: null
  },
  status: {
    type: String,
    enum: ['pickup_completed', 'completed'],
    default: 'pickup_completed'
  },
  pickupCompletedAt: {
    type: Date,
    default: null
  },
  coordinates: {
    lat: { type: Number, required: true },
    lng: { type: Number, required: true }
  },
  notes: {
    type: String,
    trim: true
  },
  completedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Index for efficient queries
transactionSchema.index({ trucker: 1, completedAt: -1 });
transactionSchema.index({ purchaseOrderId: 1 });

const Transaction = mongoose.model('Transaction', transactionSchema);

export default Transaction;
