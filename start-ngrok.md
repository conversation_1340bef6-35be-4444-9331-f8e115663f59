# Quick ngrok Setup

## Prerequisites
1. Install ngrok: `npm install -g ngrok` or download from https://ngrok.com/
2. Sign up for a free ngrok account at https://ngrok.com/
3. Get your auth token from the ngrok dashboard

## Setup Steps

### 1. Configure ngrok (one-time setup)
```bash
ngrok config add-authtoken YOUR_AUTH_TOKEN_HERE
```

### 2. Build and Start the Application
```bash
# Build the client
npm run build

# Start the server (in a separate terminal)
npm start
```

### 3. Start ngrok Tunnel
```bash
# In another terminal, start ngrok
ngrok http 5055
```

### 4. Access Your Application
- Copy the HTTPS URL from ngrok (e.g., `https://abc123.ngrok.io`)
- Open it in your browser
- The application is now accessible from anywhere!

## Features Available

### ✅ Complete Transaction System
- **Automatic Logging**: Every delivery creates a transaction record
- **Admin View**: See all transactions with photos, signatures, quantities
- **Trucker History**: Truckers can view their delivery history
- **Simplified Process**: Materials pre-populated from purchase orders

### ✅ Full Spanish Translation
- **Language Switcher**: Globe icon (🌐) in navigation
- **Complete Translation**: All text translated to Spanish
- **Persistent**: Language choice saved automatically

### ✅ Production Ready
- **Optimized Build**: Compressed assets for fast loading
- **Static Serving**: All files served from single server
- **SPA Support**: Proper routing for single-page app

## Test Users
- **Admin**: username: `admin`, password: `admin123`
- **Trucker**: username: `trucker1`, password: `trucker123`

## Troubleshooting
- **Port Issues**: Make sure port 5055 is free
- **Build Errors**: Run `npm install` in both root and client directories
- **Database**: Ensure MongoDB connection string is set in server/.env
