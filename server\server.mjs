import express from 'express'
import cors from 'cors'
import path from 'path'
import { fileURLToPath } from 'url'
import 'dotenv/config'
import './db/conn.mjs'
import authRoutes from './routes/auth.js'
import purchaseOrderRoutes from './routes/purchaseOrders.js'

const PORT = process.env.PORT || 5055
const app = express()

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

app.use(cors())
// Increase payload limit for image uploads
app.use(express.json({ limit: '50mb' }))
app.use(express.urlencoded({ limit: '50mb', extended: true }))

// Routes
app.use('/api/auth', authRoutes)
app.use('/api/purchase-orders', purchaseOrderRoutes)

// Health check endpoint
app.get('/api/ping', (req, res) => {
  console.log('Ping request received')
  res.json({ message: 'pong', timestamp: new Date().toISOString(), port: process.env.PORT || 5055 })
})

// Serve static files from client dist folder
const clientPath = path.join(__dirname, '../client/dist')
app.use(express.static(clientPath))

// SPA fallback: redirect all other GETs to index.html
app.get('*', (req, res) => {
  res.sendFile(path.join(clientPath, 'index.html'))
})

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server is running on port: ${PORT}`)
})

