import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import LanguageSwitcher from './LanguageSwitcher';

const LoginScreen: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleTruckerLogin = () => {
    navigate('/login/trucker');
  };

  const handleAdminLogin = () => {
    navigate('/login/admin');
  };

  return (
    <div className="container-fluid vh-100 d-flex align-items-center justify-content-center bg-light">
      <div className="row w-100">
        <div className="col-md-6 col-lg-4 mx-auto">
          <div className="card shadow-lg border-0">
            <div className="card-body p-5">
              <div className="text-center mb-4">
                <h1 className="h3 mb-3 fw-bold text-primary">
                  Welcome to Logistics Portal
                </h1>
                <p className="text-muted">
                  Please select your role to continue
                </p>
              </div>

              <div className="d-flex justify-content-center mb-3">
                <LanguageSwitcher />
              </div>

              <div className="d-grid gap-3">
                <button
                  type="button"
                  className="btn btn-primary btn-lg py-3"
                  onClick={handleTruckerLogin}
                >
                  <i className="bi bi-truck me-2"></i>
                  {t('auth.trucker')} {t('auth.login')}
                </button>

                <button
                  type="button"
                  className="btn btn-success btn-lg py-3"
                  onClick={handleAdminLogin}
                >
                  <i className="bi bi-person-gear me-2"></i>
                  {t('auth.admin')} {t('auth.login')}
                </button>
              </div>

              <div className="text-center mt-4">
                <small className="text-muted">
                  Secure access to your logistics dashboard
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginScreen;
