import express from 'express';
import PurchaseOrder from '../models/PurchaseOrder.js';
import Transaction from '../models/Transaction.js';
import { authenticateToken, requireAdmin, requireTrucker, requireAdminOrTrucker } from '../middleware/auth.js';

const router = express.Router();

router.get('/', authenticateToken, async (req, res) => {
  try {
    let orders;

    if (req.user.role === 'trucker') {
      console.log('=== TRUCKER API DEBUG ===');
      console.log('Trucker ID:', req.user._id);
      console.log('Trucker username:', req.user.username);

      // Step 1: Check if trucker has an active delivery location
      orders = await PurchaseOrder.find({
        'deliveryLocations.assignedTo': req.user._id,
        'deliveryLocations.status': { $in: ['pickup_accepted', 'pickup_completed', 'en_route', 'delivery_in_progress'] }
      })
        .populate('createdBy', 'username firstName lastName')
        .populate('deliveryLocations.assignedTo', 'username firstName lastName')
        .sort({ createdAt: -1 });

      console.log('Active orders found:', orders.length);

      if (!orders.length) {
        // Step 2: Show orders with at least one unassigned delivery location (status = pending)
        orders = await PurchaseOrder.find({
          'deliveryLocations.status': 'pending'
        })
          .populate('createdBy', 'username firstName lastName')
          .populate('deliveryLocations.assignedTo', 'username firstName lastName')
          .sort({ createdAt: -1 });

        console.log('Pending orders found:', orders.length);
        console.log('Pending orders details:', JSON.stringify(orders, null, 2));
      }
    } else {
      // Admins see all orders
      orders = await PurchaseOrder.find()
        .populate('createdBy', 'username firstName lastName')
        .populate('deliveryLocations.assignedTo', 'username firstName lastName')
        .sort({ createdAt: -1 });
    }

    res.json({ success: true, data: orders });
  } catch (error) {
    console.error('Error fetching purchase orders:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching purchase orders',
      error: error.message
    });
  }
});


// Get single purchase order
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const order = await PurchaseOrder.findById(req.params.id)
      .populate('createdBy', 'username firstName lastName')
      .populate('assignedTo', 'username firstName lastName');

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    // Check permissions
    if (req.user.role === 'trucker' && 
        order.status !== 'pending' && 
        !order.assignedTo?.equals(req.user._id)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.json({
      success: true,
      data: order
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching purchase order',
      error: error.message
    });
  }
});

// Create new purchase order (admin only)
router.post('/', authenticateToken, requireAdmin, async (req, res) => {
  try {
    console.log('Received order data:', req.body);
    console.log('User:', req.user);

    // Validate required fields
    const { client, title, pickupLocation, deliveryLocations, pickupDate, deliveryDate } = req.body;

    if (!client || !title || !pickupLocation || !deliveryLocations || !Array.isArray(deliveryLocations) ||
        deliveryLocations.length === 0 || !pickupDate || !deliveryDate) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        error: 'All required fields must be provided, including at least one delivery location with materials'
      });
    }

    // Transform the data to match new schema
    const orderData = {
      client: client.trim(),
      title: title.trim(),
      description: req.body.description?.trim() || '',
      pickupLocation: {
        address: pickupLocation,
        coordinates: { lat: 0, lng: 0 } // Will be set by pre-save middleware
      },
      deliveryLocations: deliveryLocations.map(location => ({
        address: location.address,
        coordinates: { lat: 0, lng: 0 }, // Will be set by pre-save middleware
        materials: location.materials.map(material => ({
          material: material.material,
          quantityOrdered: parseFloat(material.quantity),
          quantityDelivered: 0,
          unit: material.unit,
          ratePerUnit: parseFloat(material.ratePerUnit),
          isOverDelivered: false
        })),
        status: 'pending'
      })),
      pickupDate,
      deliveryDate,
      notes: req.body.notes?.trim() || '',
      createdBy: req.user._id
    };

    console.log('Creating order with data:', orderData);

    const order = new PurchaseOrder(orderData);
    await order.save();

    await order.populate('createdBy', 'username firstName lastName');

    console.log('Order created successfully:', order);

    res.status(201).json({
      success: true,
      message: 'Purchase order created successfully',
      data: order
    });
  } catch (error) {
    console.error('Error creating purchase order:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating purchase order',
      error: error.message
    });
  }
});

// Reserve pickup for delivery location (trucker only) - Step 1
router.patch('/:id/reserve-pickup/:deliveryId', authenticateToken, requireTrucker, async (req, res) => {
  try {
    console.log('Reserve pickup request received:', {
      orderId: req.params.id,
      deliveryId: req.params.deliveryId,
      userId: req.user._id,
      body: req.body
    });

    const { selectedMaterials, partialQuantities } = req.body;

    // Check if trucker already has an active delivery
    const existingActiveOrder = await PurchaseOrder.findOne({
      'deliveryLocations.assignedTo': req.user._id,
      'deliveryLocations.status': { $in: ['pickup_accepted', 'pickup_completed', 'en_route'] }
    });

    if (existingActiveOrder) {
      return res.status(400).json({
        success: false,
        message: 'You already have an active delivery. Complete it before reserving a new one.'
      });
    }

    const order = await PurchaseOrder.findById(req.params.id);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    // Find the specific delivery location
    const deliveryLocation = order.deliveryLocations.id(req.params.deliveryId);
    if (!deliveryLocation) {
      return res.status(404).json({
        success: false,
        message: 'Delivery location not found'
      });
    }

    if (deliveryLocation.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'This delivery location is no longer available'
      });
    }

    // Update the delivery location to reserved status
    deliveryLocation.status = 'pickup_accepted';
    deliveryLocation.assignedTo = req.user._id;
    deliveryLocation.acceptedAt = new Date();

    await order.save();
    await order.populate('deliveryLocations.assignedTo', 'username firstName lastName');

    res.json({
      success: true,
      message: 'Pickup reserved successfully! You can now proceed to the pickup location to complete the pickup with photo and signature.',
      data: order
    });
  } catch (error) {
    console.error('Error reserving pickup:', error);
    res.status(500).json({
      success: false,
      message: 'Error reserving pickup',
      error: error.message
    });
  }
});

// Accept pickup for delivery location (trucker only) - Step 2: Complete with photo/signature
router.patch('/:id/accept-pickup/:deliveryId', authenticateToken, requireTrucker, async (req, res) => {
  try {
    console.log('Accept pickup request received:', {
      orderId: req.params.id,
      deliveryId: req.params.deliveryId,
      userId: req.user._id,
      body: req.body
    });

    const { latitude, longitude, pickupPhoto, pickupSignature, developerMode } = req.body;

    // Validate location data (unless in developer mode)
    if (!developerMode && (!latitude || !longitude)) {
      return res.status(400).json({
        success: false,
        message: 'Location data (latitude and longitude) is required to complete pickup'
      });
    }

    // Validate pickup documentation
    if (!pickupPhoto || !pickupSignature) {
      console.log('Validation failed - missing pickup documentation:', {
        hasPickupPhoto: !!pickupPhoto,
        hasPickupSignature: !!pickupSignature
      });
      return res.status(400).json({
        success: false,
        message: 'Pickup photo and signature are required'
      });
    }

    // Find the order first
    const order = await PurchaseOrder.findById(req.params.id);
    if (!order) {
      console.log('Purchase order not found:', req.params.id);
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    // Find the specific delivery location
    const deliveryLocation = order.deliveryLocations.id(req.params.deliveryId);
    if (!deliveryLocation) {
      console.log('Delivery location not found:', req.params.deliveryId);
      return res.status(404).json({
        success: false,
        message: 'Delivery location not found'
      });
    }

    console.log('Delivery location found:', {
      id: deliveryLocation._id,
      status: deliveryLocation.status,
      assignedTo: deliveryLocation.assignedTo
    });

    // Check if delivery location is reserved for this trucker
    if (deliveryLocation.status !== 'pickup_accepted') {
      console.log('Delivery location status check failed:', {
        currentStatus: deliveryLocation.status,
        expectedStatus: 'pickup_accepted'
      });
      return res.status(400).json({
        success: false,
        message: `This delivery must be reserved first. Current status: ${deliveryLocation.status}`
      });
    }

    // Check if this delivery location is assigned to the current trucker
    if (!deliveryLocation.assignedTo || !deliveryLocation.assignedTo.equals(req.user._id)) {
      return res.status(400).json({
        success: false,
        message: 'You are not assigned to this delivery location'
      });
    }

    // Update the delivery location with pickup completion
    deliveryLocation.status = 'pickup_completed';
    deliveryLocation.pickupPhoto = pickupPhoto;
    deliveryLocation.pickupSignature = pickupSignature;
    deliveryLocation.pickupCompletedAt = new Date();

    await order.save();

    // Check if trucker is within 1km of pickup location (skip in developer mode)
    if (!developerMode && latitude && longitude) {
      const isWithinGeofence = order.isWithinPickupGeofence(latitude, longitude, 2);

      if (!isWithinGeofence) {
        return res.status(400).json({
          success: false,
          message: 'You must be within 1km of the pickup location to accept this delivery'
        });
      }
    }

    // Create a partial transaction for the pickup
    // deliveryLocation is already defined above
    const transaction = new Transaction({
      purchaseOrderId: order._id,
      orderNumber: order.orderNumber,
      trucker: req.user._id,
      deliveryLocationId: req.params.deliveryId,
      deliveryAddress: deliveryLocation.address,
      materialsDelivered: deliveryLocation.materials.map(material => ({
        material: material.material,
        quantityDelivered: 0, // Will be updated when delivery is completed
        unit: material.unit,
        ratePerUnit: material.ratePerUnit
      })),
      pickupPhoto,
      pickupSignature,
      deliveryPhoto: null, // Will be added when delivery is completed
      signature: null, // Will be added when delivery is completed
      coordinates: {
        lat: latitude || 0,
        lng: longitude || 0
      },
      status: 'pickup_completed', // Partial transaction status
      pickupCompletedAt: new Date(),
      notes: ''
    });

    await transaction.save();

    await order.populate('deliveryLocations.assignedTo', 'username firstName lastName');

    res.json({
      success: true,
      message: 'Pickup completed successfully! You can now proceed to the delivery location.',
      data: order,
      transaction: transaction
    });
  } catch (error) {
    console.error('Error accepting pickup:', error);
    res.status(500).json({
      success: false,
      message: 'Error accepting pickup',
      error: error.message
    });
  }
});

// Update purchase order status (admin only)
router.patch('/:id/status', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { status } = req.body;
    const order = await PurchaseOrder.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    order.status = status;
    await order.save();

    await order.populate('createdBy', 'username firstName lastName');
    await order.populate('assignedTo', 'username firstName lastName');

    res.json({
      success: true,
      message: 'Purchase order status updated successfully',
      data: order
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error updating purchase order status',
      error: error.message
    });
  }
});

// Complete pickup (trucker only) - for delivery location
router.patch('/:id/complete-pickup/:deliveryId', authenticateToken, requireTrucker, async (req, res) => {
  try {
    const order = await PurchaseOrder.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    // Find the specific delivery location
    const deliveryLocation = order.deliveryLocations.id(req.params.deliveryId);
    if (!deliveryLocation) {
      return res.status(404).json({
        success: false,
        message: 'Delivery location not found'
      });
    }

    if (!deliveryLocation.assignedTo?.equals(req.user._id)) {
      return res.status(403).json({
        success: false,
        message: 'You are not assigned to this delivery location'
      });
    }

    if (deliveryLocation.status !== 'pickup_accepted') {
      return res.status(400).json({
        success: false,
        message: 'Delivery location must be in pickup_accepted status to complete pickup'
      });
    }

    // Update delivery location status
    deliveryLocation.status = 'pickup_completed';
    deliveryLocation.pickupCompletedAt = new Date();

    await order.save();

    res.json({
      success: true,
      message: 'Pickup completed successfully! You can now proceed to the delivery location.',
      data: order
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error completing pickup',
      error: error.message
    });
  }
});

// Complete delivery location (trucker only)
router.patch('/:id/complete-delivery/:deliveryId', authenticateToken, requireTrucker, async (req, res) => {
  try {
    const { latitude, longitude, deliveryPhoto, deliverySignature, actualQuantities, developerMode, notes } = req.body;

    // Validate required data (unless in developer mode)
    if (!developerMode && (!latitude || !longitude)) {
      return res.status(400).json({
        success: false,
        message: 'Location data is required'
      });
    }

    if (!deliveryPhoto || !deliverySignature) {
      return res.status(400).json({
        success: false,
        message: 'Delivery photo and delivery signature are required'
      });
    }

    if (!actualQuantities || !Array.isArray(actualQuantities)) {
      return res.status(400).json({
        success: false,
        message: 'Actual delivered quantities are required'
      });
    }

    const order = await PurchaseOrder.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    // Find and validate the specific delivery location
    const deliveryLocation = order.deliveryLocations.id(req.params.deliveryId);
    if (!deliveryLocation) {
      return res.status(404).json({
        success: false,
        message: 'Delivery location not found'
      });
    }

    if (!deliveryLocation.assignedTo?.equals(req.user._id)) {
      return res.status(403).json({
        success: false,
        message: 'You are not assigned to this delivery location'
      });
    }

    if (deliveryLocation.status !== 'pickup_completed') {
      return res.status(400).json({
        success: false,
        message: 'Pickup must be completed before delivery can be completed'
      });
    }

    // Check if trucker is within geofence for this delivery location (skip in developer mode)
    if (!developerMode && latitude && longitude) {
      const isWithinGeofence = order.isWithinDeliveryGeofence(req.params.deliveryId, latitude, longitude, 2);

      if (!isWithinGeofence) {
        return res.status(400).json({
          success: false,
          message: 'You must be within 1km of the delivery location to complete delivery'
        });
      }
    }

    // Update the specific delivery location (already validated above)

    // Find the existing partial transaction for this delivery
    const transaction = await Transaction.findOne({
      deliveryLocationId: req.params.deliveryId,
      trucker: req.user._id,
      status: 'pickup_completed'
    });

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'No pickup transaction found for this delivery. Please accept pickup first.'
      });
    }

    // Update materials delivered based on actual quantities
    const materialsDelivered = deliveryLocation.materials.map((material, index) => {
      const actualQty = actualQuantities[index] ? parseFloat(actualQuantities[index]) : 0;
      return {
        material: material.material,
        quantityDelivered: actualQty,
        unit: material.unit,
        ratePerUnit: material.ratePerUnit
      };
    });

    // Update the transaction with delivery information
    transaction.materialsDelivered = materialsDelivered;
    transaction.deliveryPhoto = deliveryPhoto;
    transaction.deliverySignature = deliverySignature;
    transaction.status = 'completed';
    transaction.completedAt = new Date();
    transaction.notes = notes || '';
    transaction.coordinates = {
      lat: latitude || 0,
      lng: longitude || 0
    };

    await transaction.save();

    // Update material quantities in the delivery location
    let hasOverDelivery = false;
    for (let i = 0; i < deliveryLocation.materials.length; i++) {
      const material = deliveryLocation.materials[i];
      const actualQty = actualQuantities[i] ? parseFloat(actualQuantities[i]) : 0;

      material.quantityDelivered += actualQty;

      // Check for over-delivery
      if (material.quantityDelivered > material.quantityOrdered) {
        material.isOverDelivered = true;
        hasOverDelivery = true;
      }
    }

    // Check if all materials for this location are fully delivered
    const allMaterialsDelivered = deliveryLocation.materials.every(material =>
      material.quantityDelivered >= material.quantityOrdered
    );

    if (allMaterialsDelivered) {
      deliveryLocation.status = 'completed';
      deliveryLocation.completedAt = new Date();
    }

    // Check if all delivery locations are completed
    const allLocationsCompleted = order.deliveryLocations.every(loc => loc.status === 'completed');

    if (allLocationsCompleted) {
      order.status = 'delivered';
    } else {
      order.status = 'delivery_in_progress';
    }

    await order.save();

    res.json({
      success: true,
      message: allLocationsCompleted ? 'All deliveries completed! Order finished.' : 'Delivery completed successfully',
      data: order,
      transaction: transaction,
      warnings: hasOverDelivery ? ['Some materials were over-delivered'] : []
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error completing delivery',
      error: error.message
    });
  }
});

// Get transaction history for trucker (only completed transactions)
router.get('/transactions', authenticateToken, requireTrucker, async (req, res) => {
  try {
    const transactions = await Transaction.find({
      trucker: req.user._id,
      status: 'completed' // Only show completed transactions to truckers
    })
      .populate('trucker', 'username firstName lastName')
      .sort({ completedAt: -1 })
      .limit(50); // Limit to last 50 transactions

    // Ensure all transactions have valid data
    const validTransactions = transactions.filter(transaction => {
      if (!transaction.trucker) {
        console.warn(`Transaction ${transaction._id} has missing trucker reference`);
        return false;
      }
      return true;
    });

    res.json({
      success: true,
      data: validTransactions
    });
  } catch (error) {
    console.error('Error fetching trucker transactions:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching transaction history',
      error: error.message
    });
  }
});

// Get all transactions for admin
router.get('/admin/transactions', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const transactions = await Transaction.find({})
      .populate('trucker', 'username firstName lastName')
      .populate('purchaseOrderId', 'orderNumber client title')
      .sort({ completedAt: -1 })
      .limit(100); // Limit to last 100 transactions

    // Filter out transactions with missing references and log them
    const validTransactions = transactions.filter(transaction => {
      if (!transaction.trucker) {
        console.warn(`Transaction ${transaction._id} has missing trucker reference`);
        return false;
      }
      if (!transaction.purchaseOrderId) {
        console.warn(`Transaction ${transaction._id} has missing purchaseOrderId reference`);
        // Still include these transactions but they'll be handled on frontend
        return true;
      }
      return true;
    });

    res.json({
      success: true,
      data: validTransactions
    });
  } catch (error) {
    console.error('Error fetching admin transactions:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching transaction history',
      error: error.message
    });
  }
});

// Delete all transactions (admin only - for testing) - MUST come before single transaction delete
router.delete('/admin/transactions/delete-all', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const result = await Transaction.deleteMany({});

    res.json({
      success: true,
      message: `Successfully deleted ${result.deletedCount} transactions`,
      deletedCount: result.deletedCount
    });
  } catch (error) {
    console.error('Error deleting all transactions:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting all transactions',
      error: error.message
    });
  }
});

// Delete single transaction (admin only)
router.delete('/admin/transactions/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const transaction = await Transaction.findByIdAndDelete(req.params.id);

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found'
      });
    }

    res.json({
      success: true,
      message: 'Transaction deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting transaction:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting transaction',
      error: error.message
    });
  }
});

// Delete all purchase orders (admin only - for testing)
router.delete('/admin/delete-all', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const result = await PurchaseOrder.deleteMany({});

    res.json({
      success: true,
      message: `Successfully deleted ${result.deletedCount} purchase orders`,
      deletedCount: result.deletedCount
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error deleting all purchase orders',
      error: error.message
    });
  }
});

// Delete purchase order (admin only)
router.delete('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const order = await PurchaseOrder.findByIdAndDelete(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    res.json({
      success: true,
      message: 'Purchase order deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error deleting purchase order',
      error: error.message
    });
  }
});

export default router;
